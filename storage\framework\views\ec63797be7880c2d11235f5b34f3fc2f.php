<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Teacher Profile','description' => 'View and manage teacher information','backRoute' => route('admin.teachers.index'),'backLabel' => 'Back to Teachers']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Teacher Profile','description' => 'View and manage teacher information','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.teachers.index')),'back-label' => 'Back to Teachers']); ?>
        
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.teachers.edit', $teacher)); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Teacher
            </a>
            
            <a href="<?php echo e(route('admin.invoices.create')); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Create Invoice
            </a>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Teacher Profile Card -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-green-500 to-green-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">
                            <?php echo e(substr($teacher->user->name, 0, 2)); ?>

                        </span>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white"><?php echo e($teacher->user->name); ?></h1>
                    <p class="text-green-100">Employee ID: <?php echo e($teacher->employee_id); ?></p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="badge <?php echo e($teacher->is_active ? 'badge-green' : 'badge-red'); ?>">
                            <?php echo e($teacher->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                        <?php if($teacher->specialization): ?>
                            <span class="text-green-100"><?php echo e($teacher->specialization); ?></span>
                        <?php endif; ?>
                        <?php if($teacher->years_of_experience > 0): ?>
                            <span class="text-green-100"><?php echo e($teacher->years_of_experience); ?> years experience</span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Invoices</dt>
                            <dd class="stat-card-value"><?php echo e($invoiceStats['total_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Paid Invoices</dt>
                            <dd class="stat-card-value"><?php echo e($invoiceStats['paid_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value"><?php echo e($invoiceStats['pending_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Draft</dt>
                            <dd class="stat-card-value"><?php echo e($invoiceStats['draft_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Present Days</dt>
                            <dd class="stat-card-value"><?php echo e($attendanceStats['present']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Absent Days</dt>
                            <dd class="stat-card-value"><?php echo e($attendanceStats['absent']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-indigo-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Attendance Rate</dt>
                            <dd class="stat-card-value"><?php echo e(number_format($attendanceStats['attendance_percentage'], 1)); ?>%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal & Professional Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->user->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Employee ID</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->employee_id); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->user->email); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->user->phone ?: 'Not provided'); ?></p>
                    </div>
                    
                    <?php if($teacher->user->address): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Address</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->user->address); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Professional Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Professional Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Qualification</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->qualification ?: 'Not specified'); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Specialization</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->specialization ?: 'Not specified'); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Hire Date</label>
                        <p class="mt-1 text-sm text-gray-900">
                            <?php echo e($teacher->hire_date ? $teacher->hire_date->format('M d, Y') : 'Not specified'); ?>

                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Years of Experience</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($teacher->years_of_experience); ?> years</p>
                    </div>
                    
                    <?php if($teacher->salary): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Monthly Salary</label>
                            <p class="mt-1 text-sm text-gray-900">RM <?php echo e(number_format($teacher->salary, 2)); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Teaching Assignments -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Teaching Assignments</h3>

                <?php if($teacher->teachingAssignments->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $teacher->teachingAssignments->groupBy('subject.name'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subjectName => $assignments): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-medium text-gray-900"><?php echo e($subjectName); ?></h4>
                                    <span class="badge badge-blue"><?php echo e($assignments->first()->subject->subject_code); ?></span>
                                </div>

                                <div class="space-y-3">
                                    <?php $__currentLoopData = $assignments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $assignment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <?php
                                            $students = $assignment->students()->with('user')->get();
                                        ?>
                                        <div class="border-l-4 border-blue-200 pl-4">
                                            <div class="flex items-center justify-between mb-2">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-gray-600 font-medium"><?php echo e($assignment->class->name); ?></span>
                                                    <?php if($assignment->section): ?>
                                                        <span class="text-gray-500">- Section <?php echo e($assignment->section->name); ?></span>
                                                    <?php endif; ?>
                                                    <?php if($assignment->is_primary): ?>
                                                        <span class="badge badge-green text-xs">Primary</span>
                                                    <?php endif; ?>
                                                </div>
                                                <span class="text-xs text-gray-500"><?php echo e($assignment->subject->credits); ?> credits</span>
                                            </div>

                                            <!-- Students in this class/section for this subject -->
                                            <?php if($students->count() > 0): ?>
                                                <div class="mt-3">
                                                    <p class="text-xs font-medium text-gray-700 mb-2">Students (<?php echo e($students->count()); ?>):</p>
                                                    <div class="flex flex-wrap gap-1">
                                                        <?php $__currentLoopData = $students->take(8); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md">
                                                                <svg class="w-3 h-3 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                                </svg>
                                                                <?php echo e($student->user->name); ?>

                                                            </span>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        <?php if($students->count() > 8): ?>
                                                            <span class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md">
                                                                +<?php echo e($students->count() - 8); ?> more
                                                            </span>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <div class="mt-3">
                                                    <p class="text-xs text-gray-500 italic">No students enrolled in this class/section for this subject</p>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>

                    <!-- Summary -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                        <div class="text-center">
                            <div class="text-lg font-semibold text-blue-600"><?php echo e($teacher->assignedSubjects->count()); ?></div>
                            <div class="text-sm text-gray-500">Subjects</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-semibold text-green-600"><?php echo e($teacher->assignedClasses->count()); ?></div>
                            <div class="text-sm text-gray-500">Classes</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-semibold text-purple-600"><?php echo e($teacher->students()->count()); ?></div>
                            <div class="text-sm text-gray-500">Students</div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No teaching assignments</h3>
                        <p class="mt-1 text-sm text-gray-500">This teacher has not been assigned to any subjects or classes yet.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Invoices -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Invoices Created</h3>
                        <a href="<?php echo e(route('admin.invoices.index', ['created_by' => $teacher->user_id])); ?>" class="text-sm text-blue-600 hover:text-blue-500">
                            View all invoices
                        </a>
                    </div>
                </div>
                
                <?php if($recentInvoices->count() > 0): ?>
                    <div class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $recentInvoices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($invoice->invoice_number); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo e($invoice->title); ?></p>
                                        <?php if($invoice->student): ?>
                                            <p class="text-xs text-gray-400">Student: <?php echo e($invoice->student->user->name); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM <?php echo e(number_format($invoice->total_amount, 2)); ?></p>
                                        <span class="badge <?php echo e($invoice->status === 'paid' ? 'badge-green' : ($invoice->status === 'overdue' ? 'badge-red' : 'badge-yellow')); ?>">
                                            <?php echo e(ucfirst($invoice->status)); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No invoices created by this teacher.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Attendance Records -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Attendance Records</h3>
                        <a href="<?php echo e(route('admin.attendance.teachers')); ?>" class="text-sm text-blue-600 hover:text-blue-500">
                            View all attendance
                        </a>
                    </div>
                </div>

                <?php if($attendanceRecords->count() > 0): ?>
                    <div class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $attendanceRecords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($attendance->date->format('M d, Y')); ?></p>
                                        <p class="text-sm text-gray-500">
                                            Marked by: <?php echo e($attendance->markedBy->name); ?>

                                        </p>
                                        <?php if($attendance->notes): ?>
                                            <p class="text-xs text-gray-400 mt-1"><?php echo e($attendance->notes); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-right">
                                        <span class="badge badge-<?php echo e($attendance->status_badge_color); ?>">
                                            <?php echo e($attendance->status_display); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No attendance records</h3>
                        <p class="mt-1 text-sm text-gray-500">This teacher has no attendance records yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Performance Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Summary</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Invoices Created:</span>
                        <span class="text-sm font-medium text-gray-900"><?php echo e($invoiceStats['total_invoices']); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Amount Invoiced:</span>
                        <span class="text-sm font-medium text-gray-900">RM <?php echo e(number_format($invoiceStats['total_amount_invoiced'], 2)); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Success Rate:</span>
                        <span class="text-sm font-medium text-green-600">
                            <?php echo e($invoiceStats['total_invoices'] > 0 ? number_format(($invoiceStats['paid_invoices'] / $invoiceStats['total_invoices']) * 100, 1) : 0); ?>%
                        </span>
                    </div>
                </div>
            </div>

            <!-- Assigned Students -->
            <?php if($assignedStudents->count() > 0): ?>
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assigned Students</h3>
                    
                    <div class="space-y-3">
                        <?php $__currentLoopData = $assignedStudents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $student): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-gray-600 font-medium text-xs">
                                            <?php echo e(substr($student->user->name, 0, 2)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($student->user->name); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo e($student->class_section); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                    
                    <div class="mt-4">
                        <a href="<?php echo e(route('admin.students.index', ['class' => implode(',', $teacher->classes ?? [])])); ?>" 
                           class="text-sm text-blue-600 hover:text-blue-500">
                            View all assigned students
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.invoices.create')); ?>" 
                       class="w-full btn-primary text-center">
                        Create Invoice
                    </a>
                    
                    <a href="<?php echo e(route('admin.teachers.edit', $teacher)); ?>" 
                       class="w-full btn-secondary text-center">
                        Edit Profile
                    </a>
                    
                    <?php if($teacher->is_active): ?>
                        <button onclick="toggleTeacherStatus('<?php echo e($teacher->id); ?>')" 
                                class="w-full bg-red-50 text-red-700 px-4 py-2 rounded-lg hover:bg-red-100 transition-colors text-center">
                            Deactivate Teacher
                        </button>
                    <?php else: ?>
                        <button onclick="toggleTeacherStatus('<?php echo e($teacher->id); ?>')" 
                                class="w-full bg-green-50 text-green-700 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors text-center">
                            Activate Teacher
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
async function toggleTeacherStatus(teacherId) {
    const action = <?php echo e($teacher->is_active ? 'false' : 'true'); ?>;
    const actionText = action ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Teacher`,
        `Are you sure you want to ${actionText} this teacher?`
    );

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo e(route('admin.teachers.toggle-status', $teacher)); ?>`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/teachers/show.blade.php ENDPATH**/ ?>