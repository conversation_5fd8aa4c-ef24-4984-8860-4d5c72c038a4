@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Teacher Profile"
        description="View and manage teacher information"
        :back-route="route('admin.teachers.index')"
        back-label="Back to Teachers">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.teachers.edit', $teacher) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Teacher
            </a>
            
            <a href="{{ route('admin.invoices.create') }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Create Invoice
            </a>
        </div>
    </x-page-header>

    <!-- Teacher Profile Card -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-green-500 to-green-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">
                            {{ substr($teacher->user->name, 0, 2) }}
                        </span>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white">{{ $teacher->user->name }}</h1>
                    <p class="text-green-100">Employee ID: {{ $teacher->employee_id }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="badge {{ $teacher->is_active ? 'badge-green' : 'badge-red' }}">
                            {{ $teacher->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        @if($teacher->specialization)
                            <span class="text-green-100">{{ $teacher->specialization }}</span>
                        @endif
                        @if($teacher->years_of_experience > 0)
                            <span class="text-green-100">{{ $teacher->years_of_experience }} years experience</span>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Invoices</dt>
                            <dd class="stat-card-value">{{ $invoiceStats['total_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Paid Invoices</dt>
                            <dd class="stat-card-value">{{ $invoiceStats['paid_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value">{{ $invoiceStats['pending_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-purple-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Draft</dt>
                            <dd class="stat-card-value">{{ $invoiceStats['draft_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Attendance Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Present Days</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['present'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Absent Days</dt>
                            <dd class="stat-card-value">{{ $attendanceStats['absent'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-indigo-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Attendance Rate</dt>
                            <dd class="stat-card-value">{{ number_format($attendanceStats['attendance_percentage'], 1) }}%</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal & Professional Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Personal Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Employee ID</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->employee_id }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->user->email }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->user->phone ?: 'Not provided' }}</p>
                    </div>
                    
                    @if($teacher->user->address)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Address</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $teacher->user->address }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Professional Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Professional Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Qualification</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->qualification ?: 'Not specified' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Specialization</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->specialization ?: 'Not specified' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Hire Date</label>
                        <p class="mt-1 text-sm text-gray-900">
                            {{ $teacher->hire_date ? $teacher->hire_date->format('M d, Y') : 'Not specified' }}
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Years of Experience</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $teacher->years_of_experience }} years</p>
                    </div>
                    
                    @if($teacher->salary)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Monthly Salary</label>
                            <p class="mt-1 text-sm text-gray-900">RM {{ number_format($teacher->salary, 2) }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Teaching Assignments -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Teaching Assignments</h3>

                @if($teacher->teachingAssignments->count() > 0)
                    <div class="space-y-4">
                        @foreach($teacher->teachingAssignments->groupBy('subject.name') as $subjectName => $assignments)
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-3">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $subjectName }}</h4>
                                    <span class="badge badge-blue">{{ $assignments->first()->subject->subject_code }}</span>
                                </div>

                                <div class="space-y-3">
                                    @foreach($assignments as $assignment)
                                        @php
                                            $students = $assignment->students()->with('user')->get();
                                        @endphp
                                        <div class="border-l-4 border-blue-200 pl-4">
                                            <div class="flex items-center justify-between mb-2">
                                                <div class="flex items-center space-x-2">
                                                    <span class="text-gray-600 font-medium">{{ $assignment->class->name }}</span>
                                                    @if($assignment->section)
                                                        <span class="text-gray-500">- Section {{ $assignment->section->name }}</span>
                                                    @endif
                                                    @if($assignment->is_primary)
                                                        <span class="badge badge-green text-xs">Primary</span>
                                                    @endif
                                                </div>
                                                <span class="text-xs text-gray-500">{{ $assignment->subject->credits }} credits</span>
                                            </div>

                                            <!-- Students in this class/section for this subject -->
                                            @if($students->count() > 0)
                                                <div class="mt-3">
                                                    <p class="text-xs font-medium text-gray-700 mb-2">Students ({{ $students->count() }}):</p>
                                                    <div class="flex flex-wrap gap-1">
                                                        @foreach($students->take(8) as $student)
                                                            <span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-md">
                                                                <svg class="w-3 h-3 mr-1 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                                                </svg>
                                                                {{ $student->user->name }}
                                                            </span>
                                                        @endforeach
                                                        @if($students->count() > 8)
                                                            <span class="inline-flex items-center px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-md">
                                                                +{{ $students->count() - 8 }} more
                                                            </span>
                                                        @endif
                                                    </div>
                                                </div>
                                            @else
                                                <div class="mt-3">
                                                    <p class="text-xs text-gray-500 italic">No students enrolled in this class/section for this subject</p>
                                                </div>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Summary -->
                    <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t border-gray-200">
                        <div class="text-center">
                            <div class="text-lg font-semibold text-blue-600">{{ $teacher->assignedSubjects->count() }}</div>
                            <div class="text-sm text-gray-500">Subjects</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-semibold text-green-600">{{ $teacher->assignedClasses->count() }}</div>
                            <div class="text-sm text-gray-500">Classes</div>
                        </div>
                        <div class="text-center">
                            <div class="text-lg font-semibold text-purple-600">{{ $teacher->students()->count() }}</div>
                            <div class="text-sm text-gray-500">Students</div>
                        </div>
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No teaching assignments</h3>
                        <p class="mt-1 text-sm text-gray-500">This teacher has not been assigned to any subjects or classes yet.</p>
                    </div>
                @endif
            </div>

            <!-- Recent Invoices -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Invoices Created</h3>
                        <a href="{{ route('admin.invoices.index', ['created_by' => $teacher->user_id]) }}" class="text-sm text-blue-600 hover:text-blue-500">
                            View all invoices
                        </a>
                    </div>
                </div>
                
                @if($recentInvoices->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($recentInvoices as $invoice)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</p>
                                        <p class="text-sm text-gray-500">{{ $invoice->title }}</p>
                                        @if($invoice->student)
                                            <p class="text-xs text-gray-400">Student: {{ $invoice->student->user->name }}</p>
                                        @endif
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</p>
                                        <span class="badge {{ $invoice->status === 'paid' ? 'badge-green' : ($invoice->status === 'overdue' ? 'badge-red' : 'badge-yellow') }}">
                                            {{ ucfirst($invoice->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No invoices created by this teacher.</p>
                    </div>
                @endif
            </div>

            <!-- Recent Attendance Records -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Attendance Records</h3>
                        <a href="{{ route('admin.attendance.teachers') }}" class="text-sm text-blue-600 hover:text-blue-500">
                            View all attendance
                        </a>
                    </div>
                </div>

                @if($attendanceRecords->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($attendanceRecords as $attendance)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $attendance->date->format('M d, Y') }}</p>
                                        <p class="text-sm text-gray-500">
                                            Marked by: {{ $attendance->markedBy->name }}
                                        </p>
                                        @if($attendance->notes)
                                            <p class="text-xs text-gray-400 mt-1">{{ $attendance->notes }}</p>
                                        @endif
                                    </div>
                                    <div class="text-right">
                                        <span class="badge badge-{{ $attendance->status_badge_color }}">
                                            {{ $attendance->status_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No attendance records</h3>
                        <p class="mt-1 text-sm text-gray-500">This teacher has no attendance records yet.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Performance Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Summary</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Invoices Created:</span>
                        <span class="text-sm font-medium text-gray-900">{{ $invoiceStats['total_invoices'] }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Amount Invoiced:</span>
                        <span class="text-sm font-medium text-gray-900">RM {{ number_format($invoiceStats['total_amount_invoiced'], 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Success Rate:</span>
                        <span class="text-sm font-medium text-green-600">
                            {{ $invoiceStats['total_invoices'] > 0 ? number_format(($invoiceStats['paid_invoices'] / $invoiceStats['total_invoices']) * 100, 1) : 0 }}%
                        </span>
                    </div>
                </div>
            </div>

            <!-- Assigned Students -->
            @if($assignedStudents->count() > 0)
                <div class="bg-white shadow rounded-lg p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Assigned Students</h3>
                    
                    <div class="space-y-3">
                        @foreach($assignedStudents as $student)
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-gray-600 font-medium text-xs">
                                            {{ substr($student->user->name, 0, 2) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ $student->user->name }}</p>
                                    <p class="text-xs text-gray-500">{{ $student->class_section }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                    
                    <div class="mt-4">
                        <a href="{{ route('admin.students.index', ['class' => implode(',', $teacher->classes ?? [])]) }}" 
                           class="text-sm text-blue-600 hover:text-blue-500">
                            View all assigned students
                        </a>
                    </div>
                </div>
            @endif

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.invoices.create') }}" 
                       class="w-full btn-primary text-center">
                        Create Invoice
                    </a>
                    
                    <a href="{{ route('admin.teachers.edit', $teacher) }}" 
                       class="w-full btn-secondary text-center">
                        Edit Profile
                    </a>
                    
                    @if($teacher->is_active)
                        <button onclick="toggleTeacherStatus('{{ $teacher->id }}')" 
                                class="w-full bg-red-50 text-red-700 px-4 py-2 rounded-lg hover:bg-red-100 transition-colors text-center">
                            Deactivate Teacher
                        </button>
                    @else
                        <button onclick="toggleTeacherStatus('{{ $teacher->id }}')" 
                                class="w-full bg-green-50 text-green-700 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors text-center">
                            Activate Teacher
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
async function toggleTeacherStatus(teacherId) {
    const action = {{ $teacher->is_active ? 'false' : 'true' }};
    const actionText = action ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Teacher`,
        `Are you sure you want to ${actionText} this teacher?`
    );

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.teachers.toggle-status', $teacher) }}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
