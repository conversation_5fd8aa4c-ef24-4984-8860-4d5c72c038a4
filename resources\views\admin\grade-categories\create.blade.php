@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Create Grade Category"
        description="Add a new grade category for assessments"
        :back-route="route('admin.grading.grade-categories.index')"
        back-label="Back to Grade Categories">
    </x-page-header>

    <!-- Grade Category Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.grade-categories.store') }}" class="space-y-6">
            @csrf

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Category Name *</label>
                    <input type="text" name="name" id="name"
                           class="form-input"
                           value="{{ old('name') }}"
                           placeholder="e.g., Midterm Exam, Final Project" required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Code -->
                <div>
                    <label for="code" class="block text-sm font-medium text-gray-700 mb-1">Category Code *</label>
                    <input type="text" name="code" id="code"
                           class="form-input"
                           value="{{ old('code') }}"
                           placeholder="e.g., MT, FP" required>
                    @error('code')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Weight -->
                <div>
                    <label for="weight" class="block text-sm font-medium text-gray-700 mb-1">Weight (%) *</label>
                    <input type="number" name="weight" id="weight"
                           class="form-input"
                           value="{{ old('weight') }}"
                           min="0" max="100" step="0.1"
                           placeholder="e.g., 30" required>
                    @error('weight')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Badge Color -->
                <div>
                    <label for="badge_color" class="block text-sm font-medium text-gray-700 mb-1">Badge Color</label>
                    <select name="badge_color" id="badge_color" class="form-select">
                        <option value="badge-blue" {{ old('badge_color') == 'badge-blue' ? 'selected' : '' }}>Blue</option>
                        <option value="badge-green" {{ old('badge_color') == 'badge-green' ? 'selected' : '' }}>Green</option>
                        <option value="badge-yellow" {{ old('badge_color') == 'badge-yellow' ? 'selected' : '' }}>Yellow</option>
                        <option value="badge-red" {{ old('badge_color') == 'badge-red' ? 'selected' : '' }}>Red</option>
                        <option value="badge-purple" {{ old('badge_color') == 'badge-purple' ? 'selected' : '' }}>Purple</option>
                        <option value="badge-indigo" {{ old('badge_color') == 'badge-indigo' ? 'selected' : '' }}>Indigo</option>
                        <option value="badge-gray" {{ old('badge_color') == 'badge-gray' ? 'selected' : '' }}>Gray</option>
                    </select>
                    @error('badge_color')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>
            </div>

            <!-- Description -->
            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <textarea name="description" id="description" rows="3"
                          class="form-textarea"
                          placeholder="Optional description of this grade category">{{ old('description') }}</textarea>
                @error('description')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Active Status -->
            <div class="flex items-center">
                <input type="checkbox" name="is_active" id="is_active"
                       class="form-checkbox" value="1" {{ old('is_active', true) ? 'checked' : '' }}>
                <label for="is_active" class="ml-2 text-sm text-gray-700">
                    Active (category will be available for use)
                </label>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                <a href="{{ route('admin.grading.grade-categories.index') }}" class="btn-cancel">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Create Category
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
