@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Page Header -->
    <x-page-header
        title="Create Grade Scale"
        description="Set up a new grading scale for assessments"
        :back-route="route('admin.grading.grade-scales.index')"
        back-label="Back to Grade Scales" />

    <!-- Create Form -->
    <div class="card">
        <form method="POST" action="{{ route('admin.grading.grade-scales.store') }}" x-data="gradeScaleForm()">
            @csrf

            <div class="space-y-6">
                <!-- Basic Information -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Scale Name</label>
                        <input type="text" 
                               name="name" 
                               id="name" 
                               value="{{ old('name') }}"
                               class="form-input @error('name') border-red-300 @enderror" 
                               placeholder="e.g., A-F Letter Grade Scale"
                               required>
                        @error('name')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">Scale Type</label>
                        <select name="type" 
                                id="type" 
                                class="form-select @error('type') border-red-300 @enderror"
                                x-model="scaleType"
                                required>
                            <option value="">Select Type</option>
                            <option value="letter" {{ old('type') === 'letter' ? 'selected' : '' }}>Letter Grade (A, B, C, etc.)</option>
                            <option value="percentage" {{ old('type') === 'percentage' ? 'selected' : '' }}>Percentage Scale</option>
                            <option value="points" {{ old('type') === 'points' ? 'selected' : '' }}>Points Scale</option>
                        </select>
                        @error('type')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Default Scale Checkbox -->
                <div class="flex items-center">
                    <input type="checkbox" 
                           name="is_default" 
                           id="is_default" 
                           value="1"
                           {{ old('is_default') ? 'checked' : '' }}
                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <label for="is_default" class="ml-2 block text-sm text-gray-900">
                        Set as default grade scale
                    </label>
                </div>

                <!-- Scale Values -->
                <div>
                    <div class="flex items-center justify-between mb-4">
                        <label class="block text-sm font-medium text-gray-700">Grade Scale Values</label>
                        <button type="button" 
                                @click="addScaleValue()" 
                                class="btn-primary text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            Add Grade
                        </button>
                    </div>

                    <div class="space-y-3" id="scale-values-container">
                        <template x-for="(scale, index) in scaleValues" :key="index">
                            <div class="grid grid-cols-1 md:grid-cols-5 gap-3 p-4 border border-gray-200 rounded-lg">
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Min %</label>
                                    <input type="number" 
                                           :name="`scale_values[${index}][min]`"
                                           x-model="scale.min"
                                           class="form-input text-sm" 
                                           min="0" 
                                           max="100" 
                                           step="0.01"
                                           required>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Max %</label>
                                    <input type="number" 
                                           :name="`scale_values[${index}][max]`"
                                           x-model="scale.max"
                                           class="form-input text-sm" 
                                           min="0" 
                                           max="100" 
                                           step="0.01"
                                           required>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Grade</label>
                                    <input type="text" 
                                           :name="`scale_values[${index}][grade]`"
                                           x-model="scale.grade"
                                           class="form-input text-sm" 
                                           placeholder="A, B, C..."
                                           required>
                                </div>
                                <div>
                                    <label class="block text-xs font-medium text-gray-700 mb-1">Points (Optional)</label>
                                    <input type="number" 
                                           :name="`scale_values[${index}][points]`"
                                           x-model="scale.points"
                                           class="form-input text-sm" 
                                           min="0" 
                                           max="5" 
                                           step="0.1"
                                           placeholder="4.0">
                                </div>
                                <div class="flex items-end">
                                    <button type="button" 
                                            @click="removeScaleValue(index)"
                                            class="w-full px-3 py-2 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors">
                                        <svg class="w-4 h-4 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </template>
                    </div>

                    @error('scale_values')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror

                    <!-- Quick Templates -->
                    <div class="mt-4 p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm font-medium text-gray-700 mb-2">Quick Templates:</p>
                        <div class="flex flex-wrap gap-2">
                            <button type="button" 
                                    @click="loadTemplate('standard')"
                                    class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200">
                                Standard A-F
                            </button>
                            <button type="button" 
                                    @click="loadTemplate('plus_minus')"
                                    class="px-3 py-1 text-xs bg-green-100 text-green-700 rounded-md hover:bg-green-200">
                                A-F with +/-
                            </button>
                            <button type="button" 
                                    @click="loadTemplate('percentage')"
                                    class="px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200">
                                Percentage
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
                    <a href="{{ route('admin.grading.grade-scales.index') }}" class="btn-cancel">
                        Cancel
                    </a>
                    <button type="submit" class="btn-primary">
                        Create Grade Scale
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<script>
function gradeScaleForm() {
    return {
        scaleType: '{{ old('type') }}',
        scaleValues: @json(old('scale_values', [])),

        init() {
            if (this.scaleValues.length === 0) {
                this.addScaleValue();
            }
        },

        addScaleValue() {
            this.scaleValues.push({
                min: '',
                max: '',
                grade: '',
                points: ''
            });
        },

        removeScaleValue(index) {
            if (this.scaleValues.length > 1) {
                this.scaleValues.splice(index, 1);
            }
        },

        loadTemplate(type) {
            switch(type) {
                case 'standard':
                    this.scaleValues = [
                        { min: 90, max: 100, grade: 'A', points: 4.0 },
                        { min: 80, max: 89, grade: 'B', points: 3.0 },
                        { min: 70, max: 79, grade: 'C', points: 2.0 },
                        { min: 60, max: 69, grade: 'D', points: 1.0 },
                        { min: 0, max: 59, grade: 'F', points: 0.0 }
                    ];
                    break;
                case 'plus_minus':
                    this.scaleValues = [
                        { min: 97, max: 100, grade: 'A+', points: 4.0 },
                        { min: 93, max: 96, grade: 'A', points: 4.0 },
                        { min: 90, max: 92, grade: 'A-', points: 3.7 },
                        { min: 87, max: 89, grade: 'B+', points: 3.3 },
                        { min: 83, max: 86, grade: 'B', points: 3.0 },
                        { min: 80, max: 82, grade: 'B-', points: 2.7 },
                        { min: 77, max: 79, grade: 'C+', points: 2.3 },
                        { min: 73, max: 76, grade: 'C', points: 2.0 },
                        { min: 70, max: 72, grade: 'C-', points: 1.7 },
                        { min: 60, max: 69, grade: 'D', points: 1.0 },
                        { min: 0, max: 59, grade: 'F', points: 0.0 }
                    ];
                    break;
                case 'percentage':
                    this.scaleValues = [
                        { min: 90, max: 100, grade: 'Excellent', points: 4.0 },
                        { min: 80, max: 89, grade: 'Very Good', points: 3.0 },
                        { min: 70, max: 79, grade: 'Good', points: 2.0 },
                        { min: 60, max: 69, grade: 'Satisfactory', points: 1.0 },
                        { min: 0, max: 59, grade: 'Needs Improvement', points: 0.0 }
                    ];
                    break;
            }
        }
    }
}
</script>
@endsection
