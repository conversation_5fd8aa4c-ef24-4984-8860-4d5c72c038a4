<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GradeScale;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GradeScaleController extends Controller
{
    /**
     * Display a listing of grade scales.
     */
    public function index()
    {
        $gradeScales = GradeScale::orderBy('is_default', 'desc')
                                ->orderBy('name')
                                ->get();

        $stats = [
            'total' => $gradeScales->count(),
            'active' => $gradeScales->where('is_active', true)->count(),
            'default' => $gradeScales->where('is_default', true)->count(),
        ];

        return view('admin.grade-scales.index', compact('gradeScales', 'stats'));
    }

    /**
     * Show the form for creating a new grade scale.
     */
    public function create()
    {
        return view('admin.grade-scales.create');
    }

    /**
     * Store a newly created grade scale.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:grade_scales',
            'type' => 'required|in:letter,percentage,points',
            'scale_values' => 'required|array|min:1',
            'scale_values.*.min' => 'required|numeric|min:0|max:100',
            'scale_values.*.max' => 'required|numeric|min:0|max:100',
            'scale_values.*.grade' => 'required|string|max:10',
            'scale_values.*.points' => 'nullable|numeric|min:0|max:5',
            'is_default' => 'boolean',
        ]);

        // Validate scale values
        $scaleValues = $request->scale_values;
        foreach ($scaleValues as $scale) {
            if ($scale['min'] > $scale['max']) {
                return back()->withErrors(['scale_values' => 'Minimum value cannot be greater than maximum value.']);
            }
        }

        DB::transaction(function () use ($request) {
            // If setting as default, remove default from others
            if ($request->is_default) {
                GradeScale::where('is_default', true)->update(['is_default' => false]);
            }

            GradeScale::create([
                'name' => $request->name,
                'type' => $request->type,
                'scale_values' => $request->scale_values,
                'is_default' => $request->boolean('is_default'),
                'is_active' => true,
            ]);
        });

        return redirect()->route('admin.grade-scales.index')
                        ->with('success', 'Grade scale created successfully.');
    }

    /**
     * Display the specified grade scale.
     */
    public function show(GradeScale $gradeScale)
    {
        $gradeScale->load(['exams', 'assignments', 'grades']);
        
        $usage = [
            'exams' => $gradeScale->exams()->count(),
            'assignments' => $gradeScale->assignments()->count(),
            'grades' => $gradeScale->grades()->count(),
        ];

        return view('admin.grade-scales.show', compact('gradeScale', 'usage'));
    }

    /**
     * Show the form for editing the specified grade scale.
     */
    public function edit(GradeScale $gradeScale)
    {
        return view('admin.grade-scales.edit', compact('gradeScale'));
    }

    /**
     * Update the specified grade scale.
     */
    public function update(Request $request, GradeScale $gradeScale)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:grade_scales,name,' . $gradeScale->id,
            'type' => 'required|in:letter,percentage,points',
            'scale_values' => 'required|array|min:1',
            'scale_values.*.min' => 'required|numeric|min:0|max:100',
            'scale_values.*.max' => 'required|numeric|min:0|max:100',
            'scale_values.*.grade' => 'required|string|max:10',
            'scale_values.*.points' => 'nullable|numeric|min:0|max:5',
            'is_default' => 'boolean',
        ]);

        // Validate scale values
        $scaleValues = $request->scale_values;
        foreach ($scaleValues as $scale) {
            if ($scale['min'] > $scale['max']) {
                return back()->withErrors(['scale_values' => 'Minimum value cannot be greater than maximum value.']);
            }
        }

        DB::transaction(function () use ($request, $gradeScale) {
            // If setting as default, remove default from others
            if ($request->is_default && !$gradeScale->is_default) {
                GradeScale::where('is_default', true)->update(['is_default' => false]);
            }

            $gradeScale->update([
                'name' => $request->name,
                'type' => $request->type,
                'scale_values' => $request->scale_values,
                'is_default' => $request->boolean('is_default'),
            ]);
        });

        return redirect()->route('admin.grade-scales.show', $gradeScale)
                        ->with('success', 'Grade scale updated successfully.');
    }

    /**
     * Remove the specified grade scale.
     */
    public function destroy(GradeScale $gradeScale)
    {
        // Check if scale is in use
        $usage = $gradeScale->exams()->count() + 
                $gradeScale->assignments()->count() + 
                $gradeScale->grades()->count();

        if ($usage > 0) {
            return back()->withErrors(['error' => 'Cannot delete grade scale that is currently in use.']);
        }

        if ($gradeScale->is_default) {
            return back()->withErrors(['error' => 'Cannot delete the default grade scale.']);
        }

        $gradeScale->delete();

        return redirect()->route('admin.grade-scales.index')
                        ->with('success', 'Grade scale deleted successfully.');
    }

    /**
     * Toggle the active status of a grade scale.
     */
    public function toggleStatus(GradeScale $gradeScale)
    {
        if ($gradeScale->is_default && $gradeScale->is_active) {
            return back()->withErrors(['error' => 'Cannot deactivate the default grade scale.']);
        }

        $gradeScale->update(['is_active' => !$gradeScale->is_active]);

        $status = $gradeScale->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Grade scale {$status} successfully.");
    }

    /**
     * Set a grade scale as default.
     */
    public function setDefault(GradeScale $gradeScale)
    {
        if (!$gradeScale->is_active) {
            return back()->withErrors(['error' => 'Cannot set inactive grade scale as default.']);
        }

        DB::transaction(function () use ($gradeScale) {
            GradeScale::where('is_default', true)->update(['is_default' => false]);
            $gradeScale->update(['is_default' => true]);
        });

        return back()->with('success', 'Grade scale set as default successfully.');
    }
}
