<?php $__env->startSection('content'); ?>
<div class="space-y-6">
    <!-- Header -->
    <?php if (isset($component)) { $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.page-header','data' => ['title' => 'Student Profile','description' => 'View and manage student information','backRoute' => route('admin.students.index'),'backLabel' => 'Back to Students']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('page-header'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['title' => 'Student Profile','description' => 'View and manage student information','back-route' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(route('admin.students.index')),'back-label' => 'Back to Students']); ?>
        
        <div class="flex items-center space-x-3">
            <a href="<?php echo e(route('admin.students.edit', $student)); ?>" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Student
            </a>
            
            <a href="<?php echo e(route('admin.invoices.create', ['student_id' => $student->id])); ?>" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Create Invoice
            </a>
        </div>
     <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $attributes = $__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__attributesOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e)): ?>
<?php $component = $__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e; ?>
<?php unset($__componentOriginalf8d4ea307ab1e58d4e472a43c8548d8e); ?>
<?php endif; ?>

    <!-- Student Profile Card -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">
                            <?php echo e(substr($student->user->name, 0, 2)); ?>

                        </span>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white"><?php echo e($student->user->name); ?></h1>
                    <p class="text-blue-100">Student ID: <?php echo e($student->student_id); ?></p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="badge <?php echo e($student->is_active ? 'badge-green' : 'badge-red'); ?>">
                            <?php echo e($student->is_active ? 'Active' : 'Inactive'); ?>

                        </span>
                        <span class="text-blue-100"><?php echo e($student->class_section); ?></span>
                        <span class="text-blue-100">Age: <?php echo e($student->age); ?></span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Invoices</dt>
                            <dd class="stat-card-value"><?php echo e($stats['total_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Paid Invoices</dt>
                            <dd class="stat-card-value"><?php echo e($stats['paid_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value"><?php echo e($stats['pending_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Overdue</dt>
                            <dd class="stat-card-value"><?php echo e($stats['overdue_invoices']); ?></dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->user->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Student ID</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->student_id); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->user->email); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->user->phone ?: 'Not provided'); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->date_of_birth->format('M d, Y')); ?> (<?php echo e($student->age); ?> years old)</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Gender</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e(ucfirst($student->gender)); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Blood Group</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->blood_group ?: 'Not specified'); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Emergency Contact</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->emergency_contact ?: 'Not provided'); ?></p>
                    </div>
                    
                    <?php if($student->user->address): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Address</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($student->user->address); ?></p>
                        </div>
                    <?php endif; ?>
                    
                    <?php if($student->medical_conditions): ?>
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Medical Conditions</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($student->medical_conditions); ?></p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Academic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Academic Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <?php if($student->currentEnrollment): ?>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Current Class</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($student->currentEnrollment->class->name); ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Current Section</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($student->currentEnrollment->section->name); ?></p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Enrollment Date</label>
                            <p class="mt-1 text-sm text-gray-900"><?php echo e($student->currentEnrollment->enrollment_date->format('M d, Y')); ?></p>
                        </div>
                    <?php else: ?>
                        <div class="md:col-span-3">
                            <div class="text-center py-4">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Not Enrolled</h3>
                                <p class="mt-1 text-sm text-gray-500">This student is not currently enrolled in any class.</p>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Roll Number</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->roll_number ?: 'Not assigned'); ?></p>
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Admission Date</label>
                        <p class="mt-1 text-sm text-gray-900"><?php echo e($student->admission_date->format('M d, Y')); ?></p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Subjects -->
            <?php if($student->currentEnrollment): ?>
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Enrolled Subjects</h3>

                <?php if($student->activeSubjects->count() > 0): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <?php $__currentLoopData = $student->activeSubjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $teachers = $student->getTeachersForSubject($subject->id);
                            ?>
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-gray-900"><?php echo e($subject->name); ?></h4>
                                    <span class="badge <?php echo e($subject->pivot->is_mandatory ?? true ? 'badge-blue' : 'badge-green'); ?>">
                                        <?php echo e($subject->pivot->is_mandatory ?? true ? 'Mandatory' : 'Elective'); ?>

                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mb-2"><?php echo e($subject->subject_code); ?></p>
                                <p class="text-xs text-gray-600 mb-3"><?php echo e($subject->description); ?></p>

                                <!-- Assigned Teachers -->
                                <?php if($teachers->count() > 0): ?>
                                    <div class="border-t border-gray-100 pt-3 mt-3">
                                        <p class="text-xs font-medium text-gray-700 mb-2">Assigned Teachers:</p>
                                        <div class="flex flex-wrap gap-1">
                                            <?php $__currentLoopData = $teachers; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $teacher): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <span class="badge <?php echo e($teacher->pivot->is_primary ?? false ? 'badge-blue' : 'badge-gray'); ?>">
                                                    <?php echo e($teacher->user->name); ?>

                                                    <?php if($teacher->pivot->is_primary ?? false): ?>
                                                        (Primary)
                                                    <?php endif; ?>
                                                </span>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <div class="border-t border-gray-100 pt-3 mt-3">
                                        <p class="text-xs text-gray-500 italic">No teachers assigned</p>
                                    </div>
                                <?php endif; ?>

                                <div class="mt-3 flex items-center justify-between">
                                    <span class="text-xs text-gray-500"><?php echo e($subject->credits); ?> credits</span>
                                    <span class="text-xs text-gray-500"><?php echo e(ucfirst($subject->category)); ?></span>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No subjects enrolled</h3>
                        <p class="mt-1 text-sm text-gray-500">This student is not enrolled in any subjects yet.</p>
                    </div>
                <?php endif; ?>
            </div>
            <?php endif; ?>

            <!-- Attendance Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Attendance Summary (Current Month)</h3>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600"><?php echo e($attendanceStats['present']); ?></div>
                        <div class="text-sm text-gray-500">Present</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600"><?php echo e($attendanceStats['absent']); ?></div>
                        <div class="text-sm text-gray-500">Absent</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600"><?php echo e($attendanceStats['excused']); ?></div>
                        <div class="text-sm text-gray-500">Excused</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600"><?php echo e($attendanceStats['attendance_percentage']); ?>%</div>
                        <div class="text-sm text-gray-500">Attendance Rate</div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm mb-2">
                        <span>Overall Attendance</span>
                        <span class="font-semibold"><?php echo e($attendanceStats['attendance_percentage']); ?>%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($attendanceStats['attendance_percentage']); ?>%"></div>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Attendance</h3>
                        <a href="<?php echo e(route('admin.attendance.students', ['class' => $student->class, 'section' => $student->section])); ?>" class="text-sm text-blue-600 hover:text-blue-500">
                            View all attendance
                        </a>
                    </div>
                </div>

                <?php if($student->attendances->count() > 0): ?>
                    <div class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $student->attendances->take(10); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $attendance): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($attendance->date->format('M d, Y')); ?></p>
                                        <p class="text-sm text-gray-500">
                                            Marked by: <?php echo e($attendance->markedBy->name); ?>

                                            <?php if($attendance->check_in_time): ?>
                                                at <?php echo e($attendance->check_in_time->format('H:i')); ?>

                                            <?php endif; ?>
                                        </p>
                                        <?php if($attendance->notes): ?>
                                            <p class="text-xs text-gray-400 mt-1"><?php echo e($attendance->notes); ?></p>
                                        <?php endif; ?>
                                    </div>
                                    <div class="text-right">
                                        <span class="badge badge-<?php echo e($attendance->status_badge_color); ?>">
                                            <?php echo e($attendance->status_display); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No attendance records found for this student.</p>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Recent Invoices -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
                        <a href="<?php echo e(route('admin.invoices.index', ['student_id' => $student->id])); ?>" class="text-sm text-blue-600 hover:text-blue-500">
                            View all invoices
                        </a>
                    </div>
                </div>
                
                <?php if($student->invoices->count() > 0): ?>
                    <div class="divide-y divide-gray-200">
                        <?php $__currentLoopData = $student->invoices; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $invoice): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900"><?php echo e($invoice->invoice_number); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo e($invoice->title); ?></p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM <?php echo e(number_format($invoice->total_amount, 2)); ?></p>
                                        <span class="badge <?php echo e($invoice->status === 'paid' ? 'badge-green' : ($invoice->status === 'overdue' ? 'badge-red' : 'badge-yellow')); ?>">
                                            <?php echo e(ucfirst($invoice->status)); ?>

                                        </span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No invoices found for this student.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Financial Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Financial Summary</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Paid:</span>
                        <span class="text-sm font-medium text-green-600">RM <?php echo e(number_format($stats['total_amount_paid'], 2)); ?></span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Amount Due:</span>
                        <span class="text-sm font-medium text-red-600">RM <?php echo e(number_format($stats['total_amount_due'], 2)); ?></span>
                    </div>
                </div>
            </div>

            <!-- Guardians -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Guardians</h3>
                
                <?php if($student->guardians->count() > 0): ?>
                    <div class="space-y-4">
                        <?php $__currentLoopData = $student->guardians; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $guardian): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-gray-600 font-medium text-sm">
                                            <?php echo e(substr($guardian->user->name, 0, 2)); ?>

                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900"><?php echo e($guardian->user->name); ?></p>
                                    <p class="text-sm text-gray-500"><?php echo e($guardian->relationship); ?></p>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                <?php else: ?>
                    <p class="text-sm text-gray-500">No guardians assigned.</p>
                <?php endif; ?>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="<?php echo e(route('admin.invoices.create', ['student_id' => $student->id])); ?>" 
                       class="w-full btn-primary text-center">
                        Create Invoice
                    </a>
                    
                    <a href="<?php echo e(route('admin.students.edit', $student)); ?>" 
                       class="w-full btn-secondary text-center">
                        Edit Profile
                    </a>
                    
                    <?php if($student->is_active): ?>
                        <button onclick="toggleStudentStatus('<?php echo e($student->id); ?>')" 
                                class="w-full bg-red-50 text-red-700 px-4 py-2 rounded-lg hover:bg-red-100 transition-colors text-center">
                            Deactivate Student
                        </button>
                    <?php else: ?>
                        <button onclick="toggleStudentStatus('<?php echo e($student->id); ?>')" 
                                class="w-full bg-green-50 text-green-700 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors text-center">
                            Activate Student
                        </button>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
async function toggleStudentStatus(studentId) {
    const action = <?php echo e($student->is_active ? 'false' : 'true'); ?>;
    const actionText = action ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Student`,
        `Are you sure you want to ${actionText} this student?`
    );

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `<?php echo e(route('admin.students.toggle-status', $student)); ?>`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '<?php echo e(csrf_token()); ?>';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\school-management-system\resources\views/admin/students/show.blade.php ENDPATH**/ ?>