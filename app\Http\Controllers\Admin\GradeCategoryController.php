<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\GradeCategory;
use Illuminate\Http\Request;

class GradeCategoryController extends Controller
{
    /**
     * Display a listing of grade categories.
     */
    public function index()
    {
        $gradeCategories = GradeCategory::ordered()->get();

        $stats = [
            'total' => $gradeCategories->count(),
            'active' => $gradeCategories->where('is_active', true)->count(),
            'total_weight' => $gradeCategories->where('is_active', true)->sum('weight_percentage'),
        ];

        return view('admin.grade-categories.index', compact('gradeCategories', 'stats'));
    }

    /**
     * Show the form for creating a new grade category.
     */
    public function create()
    {
        return view('admin.grade-categories.create');
    }

    /**
     * Store a newly created grade category.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:20|unique:grade_categories',
            'description' => 'nullable|string',
            'weight_percentage' => 'required|numeric|min:0|max:100',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'required|integer|min:0',
        ]);

        GradeCategory::create($request->all());

        return redirect()->route('admin.grade-categories.index')
                        ->with('success', 'Grade category created successfully.');
    }

    /**
     * Display the specified grade category.
     */
    public function show(GradeCategory $gradeCategory)
    {
        $gradeCategory->load(['exams', 'assignments', 'grades']);
        
        $usage = [
            'exams' => $gradeCategory->exams()->count(),
            'assignments' => $gradeCategory->assignments()->count(),
            'grades' => $gradeCategory->grades()->count(),
        ];

        return view('admin.grade-categories.show', compact('gradeCategory', 'usage'));
    }

    /**
     * Show the form for editing the specified grade category.
     */
    public function edit(GradeCategory $gradeCategory)
    {
        return view('admin.grade-categories.edit', compact('gradeCategory'));
    }

    /**
     * Update the specified grade category.
     */
    public function update(Request $request, GradeCategory $gradeCategory)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'code' => 'required|string|max:20|unique:grade_categories,code,' . $gradeCategory->id,
            'description' => 'nullable|string',
            'weight_percentage' => 'required|numeric|min:0|max:100',
            'color' => 'required|string|regex:/^#[0-9A-Fa-f]{6}$/',
            'sort_order' => 'required|integer|min:0',
        ]);

        $gradeCategory->update($request->all());

        return redirect()->route('admin.grade-categories.show', $gradeCategory)
                        ->with('success', 'Grade category updated successfully.');
    }

    /**
     * Remove the specified grade category.
     */
    public function destroy(GradeCategory $gradeCategory)
    {
        // Check if category is in use
        $usage = $gradeCategory->exams()->count() + 
                $gradeCategory->assignments()->count() + 
                $gradeCategory->grades()->count();

        if ($usage > 0) {
            return back()->withErrors(['error' => 'Cannot delete grade category that is currently in use.']);
        }

        $gradeCategory->delete();

        return redirect()->route('admin.grade-categories.index')
                        ->with('success', 'Grade category deleted successfully.');
    }

    /**
     * Toggle the active status of a grade category.
     */
    public function toggleStatus(GradeCategory $gradeCategory)
    {
        $gradeCategory->update(['is_active' => !$gradeCategory->is_active]);

        $status = $gradeCategory->is_active ? 'activated' : 'deactivated';
        return back()->with('success', "Grade category {$status} successfully.");
    }
}
