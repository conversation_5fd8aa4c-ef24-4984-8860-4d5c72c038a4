@extends('layouts.admin')

@section('title', 'Grade Category Details')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Grade Category Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.grading.grade-categories.index') }}">Grade Categories</a></li>
                    <li class="breadcrumb-item active">{{ $gradeCategory->name }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.grading.grade-categories.edit', $gradeCategory) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-edit me-1"></i> Edit
            </a>
            <a href="{{ route('admin.grading.grade-categories.index') }}" class="btn btn-white btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Category Details -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Category Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Name</label>
                                <p class="h5">{{ $gradeCategory->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Code</label>
                                <p class="h5">
                                    <span class="badge" style="background-color: {{ $gradeCategory->color }}; color: white;">
                                        {{ $gradeCategory->code }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($gradeCategory->description)
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p>{{ $gradeCategory->description }}</p>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Weight Percentage</label>
                                <p class="h5">{{ number_format($gradeCategory->weight_percentage, 1) }}%</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Sort Order</label>
                                <p class="h5">{{ $gradeCategory->sort_order }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    @if($gradeCategory->is_active)
                                        <span class="badge bg-success">Active</span>
                                    @else
                                        <span class="badge bg-danger">Inactive</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p>{{ $gradeCategory->created_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Last Updated</label>
                                <p>{{ $gradeCategory->updated_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="col-lg-4">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Usage Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Exams</span>
                            <span class="badge bg-primary">{{ $usage['exams'] }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Assignments</span>
                            <span class="badge bg-info">{{ $usage['assignments'] }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Grades</span>
                            <span class="badge bg-success">{{ $usage['grades'] }}</span>
                        </div>
                    </div>
                    <hr>
                    <div class="d-flex justify-content-between align-items-center">
                        <strong>Total Usage</strong>
                        <strong class="badge bg-dark">{{ $usage['exams'] + $usage['assignments'] + $usage['grades'] }}</strong>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="card shadow mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <form method="POST" action="{{ route('admin.grading.grade-categories.toggle-status', $gradeCategory) }}">
                            @csrf
                            <button type="submit" class="btn {{ $gradeCategory->is_active ? 'btn-warning' : 'btn-success' }} btn-sm w-100">
                                <i class="fas fa-{{ $gradeCategory->is_active ? 'pause' : 'play' }} me-1"></i>
                                {{ $gradeCategory->is_active ? 'Deactivate' : 'Activate' }}
                            </button>
                        </form>

                        @if($usage['exams'] + $usage['assignments'] + $usage['grades'] == 0)
                        <form method="POST" action="{{ route('admin.grading.grade-categories.destroy', $gradeCategory) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this grade category?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100">
                                <i class="fas fa-trash me-1"></i> Delete
                            </button>
                        </form>
                        @else
                        <button type="button" class="btn btn-danger btn-sm w-100" disabled title="Cannot delete category in use">
                            <i class="fas fa-trash me-1"></i> Delete (In Use)
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
