@extends('layouts.app')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <x-page-header
        title="Student Profile"
        description="View and manage student information"
        :back-route="route('admin.students.index')"
        back-label="Back to Students">
        
        <div class="flex items-center space-x-3">
            <a href="{{ route('admin.students.edit', $student) }}" class="btn-secondary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                </svg>
                Edit Student
            </a>
            
            <a href="{{ route('admin.invoices.create', ['student_id' => $student->id]) }}" class="btn-primary">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Create Invoice
            </a>
        </div>
    </x-page-header>

    <!-- Student Profile Card -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-2xl">
                            {{ substr($student->user->name, 0, 2) }}
                        </span>
                    </div>
                </div>
                <div class="ml-6">
                    <h1 class="text-2xl font-bold text-white">{{ $student->user->name }}</h1>
                    <p class="text-blue-100">Student ID: {{ $student->student_id }}</p>
                    <div class="flex items-center mt-2 space-x-4">
                        <span class="badge {{ $student->is_active ? 'badge-green' : 'badge-red' }}">
                            {{ $student->is_active ? 'Active' : 'Inactive' }}
                        </span>
                        <span class="text-blue-100">{{ $student->class_section }}</span>
                        <span class="text-blue-100">Age: {{ $student->age }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-blue-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Total Invoices</dt>
                            <dd class="stat-card-value">{{ $stats['total_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-green-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Paid Invoices</dt>
                            <dd class="stat-card-value">{{ $stats['paid_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-yellow-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Pending</dt>
                            <dd class="stat-card-value">{{ $stats['pending_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-card-content">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="stat-card-icon bg-red-500">
                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="ml-5 w-0 flex-1">
                        <dl>
                            <dt class="stat-card-label">Overdue</dt>
                            <dd class="stat-card-value">{{ $stats['overdue_invoices'] }}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Personal Information -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Personal Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Full Name</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->user->name }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Student ID</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->student_id }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Email</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->user->email }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Phone</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->user->phone ?: 'Not provided' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->date_of_birth->format('M d, Y') }} ({{ $student->age }} years old)</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Gender</label>
                        <p class="mt-1 text-sm text-gray-900">{{ ucfirst($student->gender) }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Blood Group</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->blood_group ?: 'Not specified' }}</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Emergency Contact</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->emergency_contact ?: 'Not provided' }}</p>
                    </div>
                    
                    @if($student->user->address)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Address</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->user->address }}</p>
                        </div>
                    @endif
                    
                    @if($student->medical_conditions)
                        <div class="md:col-span-2">
                            <label class="block text-sm font-medium text-gray-700">Medical Conditions</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->medical_conditions }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Academic Information -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Academic Information</h3>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    @if($student->currentEnrollment)
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Current Class</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->currentEnrollment->class->name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Current Section</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->currentEnrollment->section->name }}</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700">Enrollment Date</label>
                            <p class="mt-1 text-sm text-gray-900">{{ $student->currentEnrollment->enrollment_date->format('M d, Y') }}</p>
                        </div>
                    @else
                        <div class="md:col-span-3">
                            <div class="text-center py-4">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-gray-900">Not Enrolled</h3>
                                <p class="mt-1 text-sm text-gray-500">This student is not currently enrolled in any class.</p>
                            </div>
                        </div>
                    @endif

                    <div>
                        <label class="block text-sm font-medium text-gray-700">Roll Number</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->roll_number ?: 'Not assigned' }}</p>
                    </div>

                    <div class="md:col-span-2">
                        <label class="block text-sm font-medium text-gray-700">Admission Date</label>
                        <p class="mt-1 text-sm text-gray-900">{{ $student->admission_date->format('M d, Y') }}</p>
                    </div>
                </div>
            </div>

            <!-- Enrolled Subjects -->
            @if($student->currentEnrollment)
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Enrolled Subjects</h3>

                @if($student->activeSubjects->count() > 0)
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        @foreach($student->activeSubjects as $subject)
                            @php
                                $teachers = $student->getTeachersForSubject($subject->id);
                            @endphp
                            <div class="border border-gray-200 rounded-lg p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h4 class="text-sm font-medium text-gray-900">{{ $subject->name }}</h4>
                                    <span class="badge {{ $subject->pivot->is_mandatory ?? true ? 'badge-blue' : 'badge-green' }}">
                                        {{ $subject->pivot->is_mandatory ?? true ? 'Mandatory' : 'Elective' }}
                                    </span>
                                </div>
                                <p class="text-xs text-gray-500 mb-2">{{ $subject->subject_code }}</p>
                                <p class="text-xs text-gray-600 mb-3">{{ $subject->description }}</p>

                                <!-- Assigned Teachers -->
                                @if($teachers->count() > 0)
                                    <div class="border-t border-gray-100 pt-3 mt-3">
                                        <p class="text-xs font-medium text-gray-700 mb-2">Assigned Teachers:</p>
                                        <div class="flex flex-wrap gap-1">
                                            @foreach($teachers as $teacher)
                                                <span class="badge {{ $teacher->pivot->is_primary ?? false ? 'badge-blue' : 'badge-gray' }}">
                                                    {{ $teacher->user->name }}
                                                    @if($teacher->pivot->is_primary ?? false)
                                                        (Primary)
                                                    @endif
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                @else
                                    <div class="border-t border-gray-100 pt-3 mt-3">
                                        <p class="text-xs text-gray-500 italic">No teachers assigned</p>
                                    </div>
                                @endif

                                <div class="mt-3 flex items-center justify-between">
                                    <span class="text-xs text-gray-500">{{ $subject->credits }} credits</span>
                                    <span class="text-xs text-gray-500">{{ ucfirst($subject->category) }}</span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No subjects enrolled</h3>
                        <p class="mt-1 text-sm text-gray-500">This student is not enrolled in any subjects yet.</p>
                    </div>
                @endif
            </div>
            @endif

            <!-- Attendance Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-6">Attendance Summary (Current Month)</h3>

                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="text-center">
                        <div class="text-2xl font-bold text-green-600">{{ $attendanceStats['present'] }}</div>
                        <div class="text-sm text-gray-500">Present</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-red-600">{{ $attendanceStats['absent'] }}</div>
                        <div class="text-sm text-gray-500">Absent</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-blue-600">{{ $attendanceStats['excused'] }}</div>
                        <div class="text-sm text-gray-500">Excused</div>
                    </div>
                    <div class="text-center">
                        <div class="text-2xl font-bold text-purple-600">{{ $attendanceStats['attendance_percentage'] }}%</div>
                        <div class="text-sm text-gray-500">Attendance Rate</div>
                    </div>
                </div>

                <div class="mb-4">
                    <div class="flex items-center justify-between text-sm mb-2">
                        <span>Overall Attendance</span>
                        <span class="font-semibold">{{ $attendanceStats['attendance_percentage'] }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $attendanceStats['attendance_percentage'] }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Recent Attendance -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Attendance</h3>
                        <a href="{{ route('admin.attendance.students', ['class' => $student->class, 'section' => $student->section]) }}" class="text-sm text-blue-600 hover:text-blue-500">
                            View all attendance
                        </a>
                    </div>
                </div>

                @if($student->attendances->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($student->attendances->take(10) as $attendance)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $attendance->date->format('M d, Y') }}</p>
                                        <p class="text-sm text-gray-500">
                                            Marked by: {{ $attendance->markedBy->name }}
                                            @if($attendance->check_in_time)
                                                at {{ $attendance->check_in_time->format('H:i') }}
                                            @endif
                                        </p>
                                        @if($attendance->notes)
                                            <p class="text-xs text-gray-400 mt-1">{{ $attendance->notes }}</p>
                                        @endif
                                    </div>
                                    <div class="text-right">
                                        <span class="badge badge-{{ $attendance->status_badge_color }}">
                                            {{ $attendance->status_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No attendance records found for this student.</p>
                    </div>
                @endif
            </div>

            <!-- Recent Invoices -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-6 py-4 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-gray-900">Recent Invoices</h3>
                        <a href="{{ route('admin.invoices.index', ['student_id' => $student->id]) }}" class="text-sm text-blue-600 hover:text-blue-500">
                            View all invoices
                        </a>
                    </div>
                </div>
                
                @if($student->invoices->count() > 0)
                    <div class="divide-y divide-gray-200">
                        @foreach($student->invoices as $invoice)
                            <div class="px-6 py-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">{{ $invoice->invoice_number }}</p>
                                        <p class="text-sm text-gray-500">{{ $invoice->title }}</p>
                                    </div>
                                    <div class="text-right">
                                        <p class="text-sm font-medium text-gray-900">RM {{ number_format($invoice->total_amount, 2) }}</p>
                                        <span class="badge {{ $invoice->status === 'paid' ? 'badge-green' : ($invoice->status === 'overdue' ? 'badge-red' : 'badge-yellow') }}">
                                            {{ ucfirst($invoice->status) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="px-6 py-8 text-center">
                        <p class="text-sm text-gray-500">No invoices found for this student.</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Financial Summary -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Financial Summary</h3>
                
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Total Paid:</span>
                        <span class="text-sm font-medium text-green-600">RM {{ number_format($stats['total_amount_paid'], 2) }}</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-sm text-gray-600">Amount Due:</span>
                        <span class="text-sm font-medium text-red-600">RM {{ number_format($stats['total_amount_due'], 2) }}</span>
                    </div>
                </div>
            </div>

            <!-- Guardians -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Guardians</h3>
                
                @if($student->guardians->count() > 0)
                    <div class="space-y-4">
                        @foreach($student->guardians as $guardian)
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                        <span class="text-gray-600 font-medium text-sm">
                                            {{ substr($guardian->user->name, 0, 2) }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm font-medium text-gray-900">{{ $guardian->user->name }}</p>
                                    <p class="text-sm text-gray-500">{{ $guardian->relationship }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-sm text-gray-500">No guardians assigned.</p>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h3>
                
                <div class="space-y-3">
                    <a href="{{ route('admin.invoices.create', ['student_id' => $student->id]) }}" 
                       class="w-full btn-primary text-center">
                        Create Invoice
                    </a>
                    
                    <a href="{{ route('admin.students.edit', $student) }}" 
                       class="w-full btn-secondary text-center">
                        Edit Profile
                    </a>
                    
                    @if($student->is_active)
                        <button onclick="toggleStudentStatus('{{ $student->id }}')" 
                                class="w-full bg-red-50 text-red-700 px-4 py-2 rounded-lg hover:bg-red-100 transition-colors text-center">
                            Deactivate Student
                        </button>
                    @else
                        <button onclick="toggleStudentStatus('{{ $student->id }}')" 
                                class="w-full bg-green-50 text-green-700 px-4 py-2 rounded-lg hover:bg-green-100 transition-colors text-center">
                            Activate Student
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
async function toggleStudentStatus(studentId) {
    const action = {{ $student->is_active ? 'false' : 'true' }};
    const actionText = action ? 'activate' : 'deactivate';
    
    const confirmed = await confirmAction(
        `${actionText.charAt(0).toUpperCase() + actionText.slice(1)} Student`,
        `Are you sure you want to ${actionText} this student?`
    );

    if (confirmed) {
        // Create and submit form
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `{{ route('admin.students.toggle-status', $student) }}`;

        // Add CSRF token
        const csrfToken = document.createElement('input');
        csrfToken.type = 'hidden';
        csrfToken.name = '_token';
        csrfToken.value = '{{ csrf_token() }}';
        form.appendChild(csrfToken);

        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
@endsection
