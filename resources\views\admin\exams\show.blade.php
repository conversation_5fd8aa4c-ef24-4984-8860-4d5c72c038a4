@extends('layouts.admin')

@section('title', 'Exam <PERSON>')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">Exam <PERSON></h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('admin.grading.exams.index') }}">Exams</a></li>
                    <li class="breadcrumb-item active">{{ $exam->title }}</li>
                </ol>
            </nav>
        </div>
        <div>
            <a href="{{ route('admin.grading.exams.edit', $exam) }}" class="btn btn-primary btn-sm">
                <i class="fas fa-edit me-1"></i> Edit
            </a>
            <a href="{{ route('admin.grading.exams.index') }}" class="btn btn-white btn-sm">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Exam Details -->
        <div class="col-lg-8">
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Exam Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Title</label>
                                <p class="h5">{{ $exam->title }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Exam Code</label>
                                <p class="h5">
                                    <span class="badge bg-primary">{{ $exam->exam_code }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    @if($exam->description)
                    <div class="mb-3">
                        <label class="form-label text-muted">Description</label>
                        <p>{{ $exam->description }}</p>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Academic Year</label>
                                <p>{{ $exam->academicYear->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Academic Term</label>
                                <p>{{ $exam->academicTerm->name }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Grade Category</label>
                                <p>
                                    <span class="badge" style="background-color: {{ $exam->gradeCategory->color }}; color: white;">
                                        {{ $exam->gradeCategory->name }}
                                    </span>
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Grade Scale</label>
                                <p>{{ $exam->gradeScale ? $exam->gradeScale->name : 'Default' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Exam Date</label>
                                <p class="h6">{{ $exam->exam_date->format('M d, Y') }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Start Time</label>
                                <p class="h6">{{ $exam->start_time ? $exam->start_time->format('g:i A') : 'Not set' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">End Time</label>
                                <p class="h6">{{ $exam->end_time ? $exam->end_time->format('g:i A') : 'Not set' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Duration</label>
                                <p class="h6">{{ $exam->duration_minutes ? $exam->duration_minutes . ' minutes' : 'Not set' }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Total Marks</label>
                                <p class="h6">{{ number_format($exam->total_marks, 1) }}</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label text-muted">Passing Marks</label>
                                <p class="h6">{{ number_format($exam->passing_marks, 1) }}</p>
                            </div>
                        </div>
                    </div>

                    @if($exam->instructions)
                    <div class="mb-3">
                        <label class="form-label text-muted">Instructions</label>
                        <div class="border rounded p-3 bg-light">
                            {!! nl2br(e($exam->instructions)) !!}
                        </div>
                    </div>
                    @endif

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Status</label>
                                <p>
                                    @switch($exam->status)
                                        @case('draft')
                                            <span class="badge bg-secondary">Draft</span>
                                            @break
                                        @case('scheduled')
                                            <span class="badge bg-info">Scheduled</span>
                                            @break
                                        @case('active')
                                            <span class="badge bg-success">Active</span>
                                            @break
                                        @case('completed')
                                            <span class="badge bg-primary">Completed</span>
                                            @break
                                        @case('cancelled')
                                            <span class="badge bg-danger">Cancelled</span>
                                            @break
                                    @endswitch
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Published</label>
                                <p>
                                    @if($exam->is_published)
                                        <span class="badge bg-success">Published</span>
                                    @else
                                        <span class="badge bg-warning">Not Published</span>
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Created By</label>
                                <p>{{ $exam->creator->name }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label text-muted">Created</label>
                                <p>{{ $exam->created_at->format('M d, Y g:i A') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Target Classes -->
            @if($targetClasses->count() > 0)
            <div class="card shadow mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Target Classes</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($targetClasses as $class)
                        <div class="col-md-4 mb-2">
                            <span class="badge bg-info">{{ $class->name }}</span>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Exam Subjects -->
            @if($exam->examSubjects->count() > 0)
            <div class="card shadow mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Exam Subjects</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Subject</th>
                                    <th>Teacher</th>
                                    <th>Total Marks</th>
                                    <th>Passing Marks</th>
                                    <th>Duration</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($exam->examSubjects as $examSubject)
                                <tr>
                                    <td>{{ $examSubject->subject->name }}</td>
                                    <td>{{ $examSubject->teacher ? $examSubject->teacher->name : 'Not assigned' }}</td>
                                    <td>{{ number_format($examSubject->subject_total_marks, 1) }}</td>
                                    <td>{{ number_format($examSubject->subject_passing_marks, 1) }}</td>
                                    <td>{{ $examSubject->subject_duration_minutes ? $examSubject->subject_duration_minutes . ' min' : '-' }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Actions & Statistics -->
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card shadow">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <form method="POST" action="{{ route('admin.grading.exams.publish', $exam) }}">
                            @csrf
                            <button type="submit" class="btn {{ $exam->is_published ? 'btn-warning' : 'btn-success' }} btn-sm w-100">
                                <i class="fas fa-{{ $exam->is_published ? 'eye-slash' : 'eye' }} me-1"></i>
                                {{ $exam->is_published ? 'Unpublish' : 'Publish' }}
                            </button>
                        </form>

                        @if($exam->status === 'draft')
                        <form method="POST" action="{{ route('admin.grading.exams.schedule', $exam) }}">
                            @csrf
                            <button type="submit" class="btn btn-info btn-sm w-100">
                                <i class="fas fa-calendar me-1"></i> Schedule
                            </button>
                        </form>
                        @endif

                        @if(!$exam->grades()->exists())
                        <form method="POST" action="{{ route('admin.grading.exams.destroy', $exam) }}" 
                              onsubmit="return confirm('Are you sure you want to delete this exam?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger btn-sm w-100">
                                <i class="fas fa-trash me-1"></i> Delete
                            </button>
                        </form>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="card shadow mt-4">
                <div class="card-header">
                    <h6 class="m-0 font-weight-bold text-primary">Statistics</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Total Subjects</span>
                            <span class="badge bg-primary">{{ $exam->examSubjects->count() }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Target Classes</span>
                            <span class="badge bg-info">{{ $targetClasses->count() }}</span>
                        </div>
                    </div>
                    <div class="mb-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>Grades Recorded</span>
                            <span class="badge bg-success">{{ $exam->grades->count() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
